import { AstriaGenerateWithPackPromt } from "@/components/photo-packs/astria-generate-with-pack-prompt";
import { AstriaPromptService } from "@/lib/astria-prompts";
import { AstriaPromptResponse, PromptData } from "@/types/astria";

interface GenerateWithPackPromptProps {
  params: {
    packId: string;
    promptId: string;
  };
}

export default async function GenerateWithPackPrompt({
  params,
}: GenerateWithPackPromptProps) {
  const { packId, promptId } = await params;
  const astriaPromptService = new AstriaPromptService();
  const promptApiResponse =
    (await astriaPromptService.getPromptBytuneIdAndPromptId(
      packId,
      promptId
    )) as AstriaPromptResponse;

  return (
    <AstriaGenerateWithPackPromt
      promtData={promptApiResponse.data as PromptData}
    />
  );
}
